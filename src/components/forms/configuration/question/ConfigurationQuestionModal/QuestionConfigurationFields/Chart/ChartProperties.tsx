import React, { use<PERSON><PERSON>back, useContext, useMemo } from "react";
import { useOutletContext } from "react-router-dom";

import { reorderWithEdge } from "@atlaskit/pragmatic-drag-and-drop-hitbox/util/reorder-with-edge";
import { Doc, Prop } from "@automerge/automerge-repo";
import {
  Accordion,
  ColorText,
  CustomAccordionTrigger,
  Heading,
  HeadingSize,
  Inline,
  OnDrop,
  OpenCloseIcon,
  Pill,
  Stack
} from "@oneteam/onetheme";

import {
  createQuestion,
  getByPath,
  getChildQuestionTypeOptions,
  getChildQuestionsAccessor
} from "@helpers/configurationFormHelper.ts";

import { ConfigurationFormAddLine } from "@components/forms/configuration/ConfigurationFormAddLine/ConfigurationFormAddLine";
import { MultiLevelQuestions } from "@components/forms/configuration/question/ConfigurationQuestionModal/QuestionConfigurationFields/MultiLevel/MultiLevelQuestions";

import { ConfigurationFormContext } from "@pages/configuration/forms/ConfigurationFormContext";
import { DocChange } from "@pages/workspace/WorkspaceLayout.tsx";

import { useCheckQuestionDuplicateIdentifier } from "@src/hooks/uniqueIdentifier/useCheckQuestionDuplicateIdentifier";
import { Dictionary } from "@src/hooks/useDictionary.tsx";
import { FormPath } from "@src/types/Form";
import { ConfigurationFormMode } from "@src/types/FormConfiguration";
import {
  MultiLevelQuestion,
  Question,
  QuestionTypes
} from "@src/types/Question.ts";
import { WorkspaceDocument } from "@src/types/documentTypes.ts";

import { ChartQuestions } from "./ChartQuestions";

export const ChartProperties = ({
  question,
  path,
  d,
  isExpanded = true,
  showReorder,
  onChangeExpanded,
  onlyContainOneQuestion = question.type === QuestionTypes.LIST,
  mode
}: {
  question: Question;
  path: Prop[];
  d: Dictionary;
  showReorder?: boolean;
  isExpanded?: boolean;
  onChangeExpanded?: (expanded: boolean) => void;
  onlyContainOneQuestion?: boolean;
  mode: `${ConfigurationFormMode}`;
}) => {
  const childQuestionsAccessor = useMemo(
    () => "charts", //getChildQuestionsAccessor(question.type),
    [question.type]
  );

  const childQuestionTypeOptions = useMemo(
    () =>
      getChildQuestionTypeOptions(question.type, {
        forUserSelection: true
      }),
    [question.type]
  );

  const { docChange } = useOutletContext<{
    document: Doc<WorkspaceDocument>;
    docChange: DocChange;
  }>();

  const childQuestions = useMemo(() => {
    const questions =
      (question as MultiLevelQuestion).properties?.[childQuestionsAccessor] ||
      [];

    if (onlyContainOneQuestion && questions.length === 0) {
      const q = createQuestion(QuestionTypes.TEXT);
      q.identifier = `${question.id}_${childQuestionsAccessor}`;

      docChange(d => {
        const question = getByPath<MultiLevelQuestion>(d, path);
        if (!question) {
          console.error("Question not found", path);
          return;
        }
        if (!question.properties) {
          question.properties = { [childQuestionsAccessor]: [] };
        }
        if (!question.properties[childQuestionsAccessor]) {
          question.properties[childQuestionsAccessor] = [];
        }
        if (question.properties[childQuestionsAccessor].length === 0) {
          question.properties[childQuestionsAccessor].push(q);
        }
      });
    }
    return questions;
  }, [
    question,
    childQuestionsAccessor,
    docChange,
    path,
    onlyContainOneQuestion
  ]);

  console.log("Child Questions", childQuestions);

  const { updateQuestionHash, questionPath } = useContext(
    ConfigurationFormContext
  );

  const { autogenerateIdentifier } = useCheckQuestionDuplicateIdentifier();

  const addQuestion = useCallback(
    (type: QuestionTypes) => {
      const q = createQuestion(type);
      q.identifier = autogenerateIdentifier(q, q.text, question);
      docChange(d => {
        const question = getByPath<MultiLevelQuestion>(d, path);
        if (!question) {
          console.error("Question not found", path);
          return;
        }
        if (!question.properties) {
          question.properties = { [childQuestionsAccessor]: [] };
        }
        if (!question.properties[childQuestionsAccessor]) {
          question.properties[childQuestionsAccessor] = [];
        }
        question.properties[childQuestionsAccessor].push(q);
      });
      return q.id;
    },
    [docChange, path, childQuestionsAccessor, autogenerateIdentifier, question]
  );

  const removeQuestion = useCallback(
    (id: string) => {
      docChange(d => {
        const question = getByPath<MultiLevelQuestion>(d, path);
        if (!question) {
          console.error("Question not found", path, id);
          return;
        }
        if (!question.properties) {
          return;
        }
        const index =
          question.properties[childQuestionsAccessor]?.findIndex(
            q => q.id === id
          ) ?? -1;
        if (index != -1) {
          question.properties[childQuestionsAccessor]?.splice(index, 1);
        }
      });
    },
    [docChange, path, childQuestionsAccessor]
  );

  const reorderQuestions: OnDrop = ({ source, destination }) => {
    const reordered = reorderWithEdge({
      list: childQuestions,
      startIndex: source.index,
      indexOfTarget: destination.index,
      closestEdgeOfTarget: destination.closestEdgeOfTarget,
      axis: "vertical"
    });

    docChange(d => {
      const question = getByPath<MultiLevelQuestion>(d, path);
      if (!question) {
        console.error("Question not found", path);
      }

      question.properties[childQuestionsAccessor] = JSON.parse(
        JSON.stringify(reordered)
      );
    });
  };

  const handleSelectQuestion = useCallback(
    (path: FormPath) => () => {
      const newPath = path.join(".");
      console.log("Selecting question:", newPath);
      if (questionPath === newPath) {
        updateQuestionHash("none");
      } else {
        updateQuestionHash(newPath);
      }
    },
    [questionPath, updateQuestionHash]
  );

  const customTrigger: CustomAccordionTrigger = useCallback(
    ({ isOpen, onClick }) => (
      <Inline
        width="fit"
        onClick={onClick}
        style={{ cursor: "pointer" }}
        alignment="left"
        gap="050"
        position="relative"
      >
        <Inline alignment="left" gap="000">
          <Heading size={HeadingSize.XXS} color={ColorText.SECONDARY}>
            {/* {d(`ui.configuration.forms.question.${question.type}.title`)} */}
            Charts
          </Heading>
        </Inline>
        <Inline alignment="left">
          {!onlyContainOneQuestion && (
            <Pill label={`${childQuestions?.length}`} />
          )}
          <OpenCloseIcon isOpen={isOpen} />
        </Inline>
      </Inline>
    ),
    [question.type, onlyContainOneQuestion, childQuestions?.length, d]
  );

  return (
    <Stack gap="050" className="multi-level-properties">
      <Accordion
        contentOverflow="visible"
        trigger={customTrigger}
        isOpen={isExpanded}
        onOpenChange={onChangeExpanded}
      >
        <Stack gap="100" style={{ paddingLeft: "var(--spacing-000)" }}>
          CHART PROPERTIES
          <ChartQuestions
            questions={childQuestions}
            removeQuestion={onlyContainOneQuestion ? () => {} : removeQuestion}
            onClick={handleSelectQuestion}
            onDrop={onlyContainOneQuestion ? () => {} : reorderQuestions}
            path={[...path, "properties", childQuestionsAccessor]}
            selectedQuestionPath={questionPath}
            showReorder={showReorder}
            parentQuestion={question}
            onlyContainOneQuestion={onlyContainOneQuestion}
            mode={mode}
          />
          {mode === ConfigurationFormMode.EDIT && !onlyContainOneQuestion && (
            <ConfigurationFormAddLine
              questionText={d(
                `ui.configuration.forms.question.${question.type}.add`
              )}
              addQuestion={type => {
                const id = addQuestion(type);
                handleSelectQuestion([
                  ...path,
                  "properties",
                  childQuestionsAccessor,
                  id
                ])();
              }}
              questionTypes={childQuestionTypeOptions as QuestionTypes[]}
            />
          )}
        </Stack>
      </Accordion>
    </Stack>
  );
};

ChartProperties.displayName = "ChartProperties";
