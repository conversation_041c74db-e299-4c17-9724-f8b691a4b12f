import React, { useCallback } from "react";
import { useOutletContext } from "react-router-dom";

import { Box, DraggableItem, Inline, OnDrop } from "@oneteam/onetheme";

import {
  getByPath,
  getChildQuestionTypeOptions,
  isMultiLevelQuestion
} from "@helpers/configurationFormHelper";

import { ConfigurationQuestionBlock } from "@components/forms/configuration/question/ConfigurationQuestionBlock/ConfigurationQuestionBlock.tsx";

import { useConfigurationFormContent } from "@src/hooks/formConfiguration/useConfigurationFormContent";
import { FormPath } from "@src/types/Form";
import {
  ConfigurationFormDropzoneTag,
  ConfigurationFormMode
} from "@src/types/FormConfiguration.ts";
import { Question, QuestionTypes } from "@src/types/Question.ts";
import { WorkspaceDocument } from "@src/types/documentTypes";

import "./MultiLevelQuestions.scss";

export type DragAndDropContextType = {
  dropzoneTags: string[];
  path?: string;
};

export const ChartQuestions = ({
  questions,
  removeQuestion,
  onClick,
  showReorder,
  path,
  selectedQuestionPath,
  parentQuestion,
  onlyContainOneQuestion = false,
  mode
}: {
  questions: Question[];
  removeQuestion: (key: string) => void;
  onClick: (path: FormPath) => () => void;
  onDrop?: OnDrop;
  showReorder?: boolean;
  path: FormPath;
  selectedQuestionPath?: string;
  parentQuestion: Question;
  onlyContainOneQuestion?: boolean;
  mode: `${ConfigurationFormMode}`;
}) => {
  const formContent = useConfigurationFormContent({ path });
  const { document: d } = useOutletContext<{
    document: WorkspaceDocument;
  }>();

  const canDropOver = useCallback(
    (data: {
      source: {
        index: number;
        context: DragAndDropContextType;
      };
      destination: {
        index: number;
        context: DragAndDropContextType;
      };
    }) => {
      const destinationQuestion: Question = getByPath<Question>(
        d,
        data.destination.context.dropzoneTags[1].split(".")
      );
      const sourceQuestionType = data.source.context
        .dropzoneTags[2] as QuestionTypes;

      if (destinationQuestion.type === QuestionTypes.LIST) {
        return false; // Cannot drop into a LIST question
      } else if (isMultiLevelQuestion(destinationQuestion.type)) {
        const validTypes = getChildQuestionTypeOptions(
          destinationQuestion.type
        );
        return validTypes.includes(sourceQuestionType);
      }
      return true;
    },
    [d]
  );

  if (!questions.length) {
    return (
      <DraggableItem
        index={0}
        dropzoneTags={[
          ConfigurationFormDropzoneTag.MULTILEVEL,
          path.slice(0, -2).join(".")
        ]}
        canDropOver={canDropOver}
      >
        <Box width="100" padding="050" />
      </DraggableItem>
    );
  }

  return (
    <>
      {questions?.map((question, index) => {
        const qPath = [...path, question.id];
        console.log("Rendering question:", question.id, qPath);
        return (
          <DraggableItem
            index={index}
            key={question.id}
            dropzoneTags={[
              ConfigurationFormDropzoneTag.MULTILEVEL,
              path.slice(0, -2).join("."),
              question.type
            ]}
            canDropOver={data =>
              canDropOver(data) && mode === ConfigurationFormMode.EDIT
            }
          >
            <Inline
              gap="100"
              width="100"
              position="relative"
              spaceBetween={false}
              key={question.id}
            >
              <ConfigurationQuestionBlock
                style={{ width: "100%" }}
                question={question}
                onClick={onClick(qPath)}
                selectedQuestionPath={selectedQuestionPath}
                handleDelete={
                  parentQuestion.type === QuestionTypes.LIST
                    ? undefined
                    : () => removeQuestion(question.id)
                }
                handleDuplicate={
                  parentQuestion.type === QuestionTypes.LIST
                    ? undefined
                    : () => formContent?.copyQuestion(index, true)
                }
                path={qPath}
                onlyContainOneQuestion={onlyContainOneQuestion}
                showReorder={
                  parentQuestion.type === QuestionTypes.LIST
                    ? false
                    : showReorder
                }
                mode={mode}
              />
            </Inline>
          </DraggableItem>
        );
      })}
    </>
  );
};

ChartQuestions.displayName = "ChartQuestions";
