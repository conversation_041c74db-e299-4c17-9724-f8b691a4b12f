import React, { use<PERSON><PERSON>back, useMemo, useRef, useState } from "react";
import { useOutletContext } from "react-router-dom";

import { Doc } from "@automerge/automerge-repo";
import {
  Checkbox,
  DropdownItem,
  DropdownItemGroup,
  FontWeight,
  Heading,
  HeadingSize,
  Icon,
  Inline,
  Renamable,
  SplitButton,
  Stack,
  TextAreaField,
  TextField
} from "@oneteam/onetheme";

import {
  getByPath,
  initialSetupName
} from "@helpers/configurationFormHelper.ts";

// import { BooleanDefault } from "@components/forms/configuration/question/ConfigurationQuestionModal/QuestionConfigurationFields/Boolean/BooleanDefault.tsx";
// import { booleanOnChange } from "@components/forms/configuration/question/ConfigurationQuestionModal/QuestionConfigurationFields/Boolean/BooleanHelper.ts";
import { BooleanProperties } from "@components/forms/configuration/question/ConfigurationQuestionModal/QuestionConfigurationFields/Boolean/BooleanProperties.tsx";
// import { DateDefault } from "@components/forms/configuration/question/ConfigurationQuestionModal/QuestionConfigurationFields/Date/DateDefault.tsx";
// import { dateOnChange } from "@components/forms/configuration/question/ConfigurationQuestionModal/QuestionConfigurationFields/Date/DateHelper.ts";
import { DateProperties } from "@components/forms/configuration/question/ConfigurationQuestionModal/QuestionConfigurationFields/Date/DateProperties.tsx";
import { FilesProperties } from "@components/forms/configuration/question/ConfigurationQuestionModal/QuestionConfigurationFields/Files/FilesProperties";
import { MultiLevelProperties } from "@components/forms/configuration/question/ConfigurationQuestionModal/QuestionConfigurationFields/MultiLevel/MultiLevelProperties";
// import { NumberDefault } from "@components/forms/configuration/question/ConfigurationQuestionModal/QuestionConfigurationFields/Number/NumberDefault.tsx";
// import { numberOnChange } from "@components/forms/configuration/question/ConfigurationQuestionModal/QuestionConfigurationFields/Number/NumberHelper.ts";
import { NumberProperties } from "@components/forms/configuration/question/ConfigurationQuestionModal/QuestionConfigurationFields/Number/NumberProperties.tsx";
import { LabelledDivider } from "@components/shared/LabelledDivider";
import { UniqueIdentifierField } from "@components/shared/UniqueIdentifier";

import { DocChange } from "@pages/workspace/WorkspaceLayout.tsx";

import { commonErrors } from "@src/constants/errorMessages";
import { useConfigurationFormError } from "@src/hooks/formConfiguration/useConfigurationFormError";
import { useCheckQuestionDuplicateIdentifier } from "@src/hooks/uniqueIdentifier/useCheckQuestionDuplicateIdentifier";
import { useDictionary } from "@src/hooks/useDictionary";
import { FormPath } from "@src/types/Form";
import { ConfigurationFormMode } from "@src/types/FormConfiguration";
import { Question, QuestionTypes } from "@src/types/Question.ts";
import {
  BooleanQuestionProperties,
  DateQuestionProperties,
  FilesQuestionProperties,
  ListQuestionProperties,
  NumberQuestionProperties,
  Placeholder,
  SelectQuestionProperties,
  TextQuestionProperties
} from "@src/types/QuestionProperties.ts";
import { WorkspaceDocument } from "@src/types/documentTypes.ts";

import { ListProperties } from "./List/ListProperties";
// import { SelectDefault } from "./Select/SelectDefault";
// import { selectOnChange } from "./Select/SelectHelper";
import { SelectProperties } from "./Select/SelectProperties";
// import { TextDefault } from "./Text/TextDefault";
// import { textOnChange } from "./Text/TextHelper";
import { TextProperties } from "./Text/TextProperties";

export const ConfigurationQuestionFields = ({
  question,
  path,
  disabled,
  parentQuestion
}: {
  question: Question;
  path: FormPath;
  disabled?: boolean;
  parentQuestion?: Question;
}) => {
  console.log(
    "ConfigurationQuestionFields",
    question,
    path,
    disabled,
    parentQuestion
  );
  // For multi-level questions
  const [isExpanded, setIsExpanded] = useState(true);

  const { docChange } = useOutletContext<{
    document: Doc<WorkspaceDocument>;
    docChange: DocChange;
  }>();
  const d = useDictionary();

  const questionAccessor = useCallback(
    (accessor: string) => {
      return `${path.join(".")}.${accessor}`;
    },
    [path]
  );

  const { checkDuplicateIdentifier, autogenerateIdentifier } =
    useCheckQuestionDuplicateIdentifier();

  const autoGeneratedIdentifier = useMemo(
    () => autogenerateIdentifier(question, question?.text, parentQuestion),
    [autogenerateIdentifier, question, parentQuestion]
  );

  const identifierSeqId = useRef(0);

  const disableIdentifierDescription = useMemo(
    () => !(parentQuestion && parentQuestion.type === QuestionTypes.LIST),
    [parentQuestion]
  );

  // const textDefaultValueOnChange = useMemo(
  //   () => textOnChange({ docChange, path, field: "defaultValue" }),
  //   [docChange, path]
  // );

  // const numberDefaultValueOnChange = useMemo(
  //   () => numberOnChange({ docChange, path, field: "defaultValue" }),
  //   [docChange, path]
  // );

  // const dateDefaultValueOnChange = useMemo(
  //   () => dateOnChange({ docChange, path, field: "defaultValue" }),
  //   [docChange, path]
  // );

  // const selectDefaultValueOnChange = useMemo(
  //   () => selectOnChange({ docChange, path, field: "defaultValue" }),
  //   [docChange, path]
  // );

  // const booleanDefaultValueOnChange = useMemo(
  //   () => booleanOnChange({ docChange, path, field: "defaultValue" }),
  //   [docChange, path]
  // );

  // Thinking EVERY time we have switches it's probably better to have a separate component pieces for each type of question
  // driven through some configuration so we don't need to update all the components every time we tweak things
  // NB: kinda dislike having to cast properties. need to think of a nicer way
  const customPropertiesFields = useMemo(() => {
    switch (question.type) {
      case QuestionTypes.TEXT: {
        return (
          <TextProperties
            question={question as Question<TextQuestionProperties>}
            path={path}
            disabled={disabled}
          />
        );
      }
      case QuestionTypes.NUMBER: {
        return (
          <NumberProperties
            question={question as Question<NumberQuestionProperties>}
            path={path}
            disabled={disabled}
          />
        );
      }
      case QuestionTypes.DATE: {
        return (
          <DateProperties
            question={question as Question<DateQuestionProperties>}
            path={path}
            disabled={disabled}
          />
        );
      }
      case QuestionTypes.MULTISELECT:
      case QuestionTypes.SELECT: {
        return (
          <SelectProperties
            question={question as Question<SelectQuestionProperties>}
            path={path}
            disabled={disabled}
          />
        );
      }
      case QuestionTypes.BOOLEAN: {
        return (
          <BooleanProperties
            question={question as Question<BooleanQuestionProperties>}
            path={path}
            disabled={disabled}
            d={d}
          />
        );
      }
      // MultiLevelQuestions
      case QuestionTypes.TABLE:
      case QuestionTypes.JSON: {
        return (
          <MultiLevelProperties
            question={question}
            path={path}
            d={d}
            showReorder
            isExpanded={isExpanded}
            onChangeExpanded={setIsExpanded}
            mode={ConfigurationFormMode.EDIT}
          />
        );
      }
      case QuestionTypes.FILES: {
        return (
          <FilesProperties
            question={question as Question<FilesQuestionProperties>}
            path={path}
            disabled={disabled}
          />
        );
      }
      case QuestionTypes.LIST: {
        return (
          <ListProperties
            question={question as Question<ListQuestionProperties>}
            path={path}
            d={d}
            isExpanded={isExpanded}
            onChangeExpanded={setIsExpanded}
            disabled={disabled}
          />
        );
      }
      case QuestionTypes.SCHEMA: {
        return <></>;
      }
      default:
        return <>Properties not implemented</>;
    }
  }, [question, path, disabled, d, isExpanded]);

  const placeholderField = useMemo(() => {
    switch (question.type) {
      case QuestionTypes.TEXT:
      case QuestionTypes.NUMBER:
      case QuestionTypes.SELECT: {
        const q = question as Question<Placeholder>;
        return (
          <TextField
            width="100"
            label={d("ui.configuration.forms.question.placeholder.label")}
            name={questionAccessor("properties.placeholder")}
            value={q.properties?.placeholder}
            disabled={disabled}
            onChange={placeholder => {
              docChange(d => {
                const q = getByPath<Question<TextQuestionProperties>>(d, path);
                if (!q || !q.properties) {
                  console.error("Question not found", path);
                  return;
                }
                q.properties.placeholder = placeholder;
              });
            }}
            onlyTriggerChangeWhenBlur
          />
        );
      }
      default:
        return undefined;
    }
  }, [d, disabled, docChange, path, question, questionAccessor]);

  // const defaultValueField = useMemo(() => {
  //   switch (question.type) {
  //     case QuestionTypes.TEXT: {
  //       return (
  //         <TextDefault
  //           question={question as Question<TextQuestionProperties>}
  //           name={questionAccessor("properties.defaultValue")}
  //           onChange={textDefaultValueOnChange}
  //           disabled={disabled}
  //           d={d}
  //           path={path}
  //         />
  //       );
  //     }
  //     case QuestionTypes.NUMBER:
  //       return (
  //         <NumberDefault
  //           question={question as Question<NumberQuestionProperties>}
  //           name={questionAccessor("properties.defaultValue")}
  //           onChange={numberDefaultValueOnChange}
  //           disabled={disabled}
  //           d={d}
  //           path={path}
  //         />
  //       );
  //     case QuestionTypes.DATE:
  //       return (
  //         <DateDefault
  //           question={question as Question<DateQuestionProperties>}
  //           name={questionAccessor("properties.defaultValue")}
  //           onChange={dateDefaultValueOnChange}
  //           disabled={disabled}
  //           d={d}
  //           path={path}
  //         />
  //       );
  //     case QuestionTypes.BOOLEAN:
  //       return (
  //         <BooleanDefault
  //           question={question as Question<BooleanQuestionProperties>}
  //           onChange={booleanDefaultValueOnChange}
  //           disabled={disabled}
  //           d={d}
  //         />
  //       );
  //     case QuestionTypes.SELECT: {
  //       return (
  //         <SelectDefault
  //           question={question as Question<SelectQuestionProperties>}
  //           name={questionAccessor("properties.defaultValue")}
  //           disabled={disabled}
  //           onChange={selectDefaultValueOnChange}
  //           d={d}
  //         />
  //       );
  //     }
  //     case QuestionTypes.UPLOAD_DOCUMENT: {
  //       return <></>;
  //     }
  //     case QuestionTypes.TABLE:
  //     case QuestionTypes.JSON:
  //       return undefined;
  //     default:
  //       return <>Default value not implemented</>;
  //   }
  // }, [
  //   booleanDefaultValueOnChange,
  //   d,
  //   dateDefaultValueOnChange,
  //   disabled,
  //   numberDefaultValueOnChange,
  //   question,
  //   questionAccessor,
  //   selectDefaultValueOnChange,
  //   textDefaultValueOnChange,
  //   path
  // ]);

  const { getServerError } = useConfigurationFormError(path);

  const getErrors = useCallback(
    (field: "name" | "identifier") => {
      const serverError = getServerError(field);
      if (serverError) {
        return serverError.message;
      }
      switch (field) {
        case "name": {
          return !question.text?.length
            ? d(commonErrors.required, {
                name: d("ui.configuration.forms.question.text.label")
              })
            : undefined;
        }
        case "identifier": {
          if (!question.identifier?.length) {
            return d(commonErrors.required, {
              name: d("ui.configuration.forms.question.identifier.label")
            });
          }
          if (checkDuplicateIdentifier(question)) {
            return d(commonErrors.duplicate, {
              name: d("ui.configuration.forms.question.identifier.label")
            });
          }
          return undefined;
        }
        default:
          return undefined;
      }
    },
    [checkDuplicateIdentifier, d, question, getServerError]
  );

  return (
    <Stack gap="050" contentsWidth="100" height="100" overflow="auto">
      test
      {disableIdentifierDescription && (
        <Heading size={HeadingSize.S} weight={FontWeight.MEDIUM}>
          <Renamable
            required
            controlFocus={question.text === initialSetupName}
            disabled={disabled}
            value={question?.text}
            error={getErrors("name")}
            onChange={text => {
              if (text?.length > 500) {
                return;
              }
              docChange(d => {
                const q = getByPath<Question>(d, path);
                if (!q) {
                  console.error("Question not found", path);
                  return;
                }

                const previousText = q.text;
                const canAutomaticallyUpdateIdentifier =
                  q.identifier
                    ?.toLowerCase()
                    .startsWith(previousText?.toLowerCase() + "_") ||
                  q.identifier?.toLowerCase() ==
                    autogenerateIdentifier(q, previousText)?.toLowerCase();

                if (canAutomaticallyUpdateIdentifier) {
                  q.identifier = autogenerateIdentifier(
                    q,
                    text,
                    parentQuestion
                  );
                }
                q.text = text;
              });
            }}
          />
        </Heading>
      )}
      <Stack
        gap="150"
        contentsWidth="100"
        height="100"
        overflow="auto"
        style={{
          padding: "var(--spacing-100) 0"
        }}
      >
        {disableIdentifierDescription && (
          <UniqueIdentifierField
            width="100"
            label={d("ui.configuration.forms.question.identifier.label")}
            description={d(
              "ui.configuration.forms.question.identifier.description"
            )}
            name={questionAccessor("identifier")}
            value={question.identifier}
            maxLength={100}
            placeholder={question.identifier}
            valueSequenceId={identifierSeqId.current}
            error={getErrors("identifier")}
            onChange={value => {
              docChange(d => {
                const q = getByPath(d, path) as Question;
                if (!q) {
                  console.error("Question not found", path);
                  return;
                }
                if (!value?.length) {
                  q.identifier = autoGeneratedIdentifier;
                  identifierSeqId.current = Date.now();
                } else {
                  q.identifier = value ?? "";
                }
              });
            }}
            onRefresh={() => {
              docChange(d => {
                const q = getByPath(d, path) as Question;

                if (!q) {
                  console.error("Question not found", path);

                  return;
                }

                q.identifier = autoGeneratedIdentifier;
              });
            }}
            required
            disabled={disabled}
            onlyTriggerChangeWhenBlur
            configPath={path}
            autoGeneratedIdentifier={autoGeneratedIdentifier}
            identifier={question.identifier}
          />
        )}
        {disableIdentifierDescription && (
          <TextAreaField
            label={d("ui.configuration.forms.question.description.label")}
            name={questionAccessor("description")}
            value={question.description}
            maxLength={255}
            onChange={description => {
              docChange(d => {
                const q = getByPath<Question>(d, path);
                if (!q) {
                  console.error("Question not found", path);
                  return;
                }
                q.description = description;
              });
            }}
            disabled={disabled}
            onlyTriggerChangeWhenBlur
          />
        )}
        {/* Custom type fields, dependent on question type */}
        {customPropertiesFields}
        {placeholderField && (
          <Inline gap="150">
            {placeholderField ?? <Inline width="100" />}
            {/* {defaultValueField ?? <Inline width="100" />} */}
          </Inline>
        )}
        {/* end section dependent on question type */}
        {/* <Toggle
          label={d(
            "ui.configuration.forms.question.allowReuseAcrossForms.label"
          )}
          description={d(
            "ui.configuration.forms.question.allowReuseAcrossForms.description"
          )}
          name={questionAccessor("properties.allowReuseAcrossForms")}
          value={Boolean(question.properties?.allowReuseAcrossForms).toString()}
          isChecked={question.properties?.allowReuseAcrossForms}
          disabled={disabled}
          onChange={(allowReuseAcrossForms: boolean | undefined) => {
            docChange(d => {
              const q = getByPath<Question>(d, path);
              if (!q || !q.properties) {
                console.error("Question not found", path);
                return;
              }
              q.properties.allowReuseAcrossForms = allowReuseAcrossForms;
            });
          }}
        /> */}
        {parentQuestion?.type !== QuestionTypes.LIST && (
          <>
            <LabelledDivider
              label={d(
                "ui.configuration.forms.question.defaultRequiredHiddenDisabled.title"
              )}
            />
            <Stack width="100" alignment="left" gap="100">
              <Checkbox
                label={d("ui.configuration.forms.question.required.label")}
                description={d(
                  "ui.configuration.forms.question.required.description"
                )}
                name={questionAccessor("properties.required")}
                value={Boolean(question.properties?.required).toString()}
                isChecked={question.properties?.required}
                disabled={disabled}
                onChange={(required: boolean) => {
                  docChange(d => {
                    const q = getByPath<Question>(d, path);
                    if (!q || !q.properties) {
                      console.error("Question not found", path);
                      return;
                    }
                    q.properties.required = required;
                  });
                }}
              />
              <Inline gap="050" alignment="left">
                <Checkbox
                  label={d("ui.configuration.forms.question.hidden.label")}
                  description={d(
                    "ui.configuration.forms.question.hidden.description"
                  )}
                  name={questionAccessor("properties.hidden")}
                  value={Boolean(question.properties?.hidden).toString()}
                  isChecked={question.properties?.hidden}
                  disabled={disabled}
                  onChange={(hidden: boolean | undefined) => {
                    docChange(d => {
                      const q = getByPath<Question>(d, path);
                      if (!q || !q.properties) {
                        console.error("Question not found", path);
                        return;
                      }
                      q.properties.hidden = hidden;
                    });
                  }}
                />
                <Icon name="visibility_off" size="m" color="text-secondary" />
              </Inline>
              <Inline gap="050" alignment="left">
                <Checkbox
                  label={d("ui.configuration.forms.question.disabled.label")}
                  description={d(
                    "ui.configuration.forms.question.disabled.description"
                  )}
                  name={questionAccessor("properties.disabled")}
                  value={Boolean(question.properties?.disabled).toString()}
                  isChecked={question.properties?.disabled}
                  disabled={disabled}
                  onChange={(isDisabled: boolean | undefined) => {
                    docChange(d => {
                      const q = getByPath<Question>(d, path);
                      if (!q || !q.properties) {
                        console.error("Question not found", path);
                        return;
                      }
                      q.properties.disabled = isDisabled;
                    });
                  }}
                />
                <Icon name="edit_off" size="m" color="text-secondary" />
              </Inline>
            </Stack>
          </>
        )}
        {question?.type === QuestionTypes.TABLE && (
          <Stack width="100" alignment="left" gap="100">
            <SplitButton
              label={"Add Chart View"}
              isOpen={true}
              onOpenChange={() => {}}
              onClick={() => {}}
              size="default"
              variant="secondary"
              leftIcon={{ name: "add" }}
            >
              <DropdownItemGroup>
                <DropdownItem
                  id="current-form"
                  key="current-form"
                  onClick={() => {}}
                  // leftElement={<Icon {...commonIcons.forms} />}
                >
                  line
                </DropdownItem>
                <DropdownItem
                  id="form"
                  key="form"
                  // onClick={onClickWithClose(nextForm.onClick)}
                  // leftElement={<Icon {...commonIcons.forms} />}
                >
                  bar
                </DropdownItem>
                <DropdownItem
                  id="foundation"
                  key="foundation"
                  // onClick={onClickWithClose(nextFoundation.onClick)}
                  // leftElement={<Icon {...commonIcons.foundations} />}
                >
                  pie
                </DropdownItem>
              </DropdownItemGroup>
            </SplitButton>
          </Stack>
        )}
      </Stack>
    </Stack>
  );
};
