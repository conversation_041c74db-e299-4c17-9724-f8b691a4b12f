import React, { Fragment, useCallback } from "react";
import { useOutletContext } from "react-router-dom";

import { reorderWithEdge } from "@atlaskit/pragmatic-drag-and-drop-hitbox/util/reorder-with-edge";
import { Doc, Prop } from "@automerge/automerge-repo";
import {
  Box,
  Button,
  ButtonGroup,
  DragAndDrop,
  DraggableItem,
  OnDrop
} from "@oneteam/onetheme";

import {
  getByPath,
  getChildQuestionsAccessor,
  getQuestions,
  isSection
} from "@helpers/configurationFormHelper.ts";

import { DocChange } from "@pages/workspace/WorkspaceLayout.tsx";

import { useConfigurationFormContent } from "@src/hooks/formConfiguration/useConfigurationFormContent.tsx";
import {
  ConfigurationFormDropzoneTag,
  ConfigurationFormMode,
  Section
} from "@src/types/FormConfiguration.ts";
import { MultiLevelQuestion, Question } from "@src/types/Question.ts";
import { WorkspaceDocument } from "@src/types/documentTypes.ts";

import { ConfigurationFormAddLine } from "./ConfigurationFormAddLine/ConfigurationFormAddLine.tsx";
import "./ConfigurationFormContent.scss";
import { ConfigurationFormSection } from "./ConfigurationFormSection.tsx";

export const ConfigurationFormContent = ({
  content,
  path,
  level
}: {
  content: Section[];
  path: Prop[];
  level: number;
}) => {
  const { docChange } = useOutletContext<{
    document: Doc<WorkspaceDocument>;
    docChange: DocChange;
  }>();

  const { addFirst, addQuestion, addSection, copySection, removeItem, mode } =
    useConfigurationFormContent({
      content,
      path,
      level
    });

  const handleSectionAndQuestionDrop: OnDrop = useCallback(
    ({ source, destination }) => {
      if (!destination) {
        return;
      }

      docChange(doc => {
        const sourceLocation = getByPath<Section | MultiLevelQuestion>(
          doc,
          source.context.dropzoneTags[1].split(".")
        );
        const destinationLocation = getByPath<Section | MultiLevelQuestion>(
          doc,
          destination.context.dropzoneTags[1].split(".")
        );

        const sourceQuestions = getQuestions(sourceLocation);
        const destinationQuestions = getQuestions(destinationLocation);

        if (!sourceQuestions || !destinationQuestions) {
          console.error("Question not found");
          return;
        }

        if (
          source.context.dropzoneTags[1] === destination.context.dropzoneTags[1]
        ) {
          // Same location - Reorder questions
          const reordered: Question[] = reorderWithEdge({
            list: JSON.parse(JSON.stringify(destinationQuestions)),
            startIndex: source.index,
            indexOfTarget: destination.index,
            closestEdgeOfTarget: destination.closestEdgeOfTarget,
            axis: "vertical"
          });

          if (isSection(destinationLocation)) {
            destinationLocation.content = reordered;
          } else {
            const childQuestionsAccessor = getChildQuestionsAccessor(
              destinationLocation.type
            );
            destinationLocation.properties[childQuestionsAccessor] = reordered;
          }
        } else {
          // Different location - Remove from source, insert at destination
          const [movedQuestion] = JSON.parse(
            JSON.stringify(sourceQuestions)
          ).splice(source.index, 1);
          sourceQuestions.splice(source.index, 1);
          destinationQuestions.splice(
            destination.closestEdgeOfTarget === "bottom" &&
              destinationQuestions.length !== 0
              ? destination.index + 1
              : destination.index,
            0,
            movedQuestion
          );
        }
      });
    },
    [docChange]
  );

  if (level === 0 && !content?.length) {
    return (
      <Box alignment="center">
        <ButtonGroup>
          <Button label="Add Question" onClick={addFirst} />
        </ButtonGroup>
      </Box>
    );
  }

  return (
    <>
      CONTENT TOP LEVEL
      {mode === ConfigurationFormMode.EDIT && (
        <ConfigurationFormAddLine
          key="add-line--1"
          addQuestion={addQuestion(0)}
          addSection={addSection(0)}
        />
      )}
      <DragAndDrop
        onDrop={handleSectionAndQuestionDrop}
        dropzoneTags={[
          ConfigurationFormDropzoneTag.SECTION,
          ConfigurationFormDropzoneTag.SUBSECTION,
          ConfigurationFormDropzoneTag.REGULAR,
          ConfigurationFormDropzoneTag.MULTILEVEL
        ]}
        className="configuration-form-questions"
      >
        {content.map((section, index) => (
          <DraggableItem
            key={`section-${section.id}`}
            index={index}
            dropzoneTags={[
              ConfigurationFormDropzoneTag.SECTION,
              path.slice(0, -1).join(".")
            ]}
            canDropOver={data => {
              return (
                data.source.context.dropzoneTags[0] ===
                ConfigurationFormDropzoneTag.SECTION
              );
            }}
          >
            <Fragment key={`section-${index}`}>
              <ConfigurationFormSection
                section={section}
                path={[...path, index]}
                copySection={() => copySection(index)}
                removeSection={() => removeItem(index)}
                showReorder={true}
              />
              {mode === ConfigurationFormMode.EDIT && (
                <ConfigurationFormAddLine
                  addQuestion={addQuestion(index + 1)}
                  addSection={addSection(index + 1)}
                />
              )}
            </Fragment>
          </DraggableItem>
        ))}
      </DragAndDrop>
    </>
  );
};

ConfigurationFormContent.displayName = "ConfigurationFormContent";
