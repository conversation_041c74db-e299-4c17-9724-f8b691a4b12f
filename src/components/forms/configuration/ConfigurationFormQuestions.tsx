import React, { useCallback, useContext } from "react";

import { Prop } from "@automerge/automerge-repo";
import { Box, DraggableItem, Inline } from "@oneteam/onetheme";

import { ConfigurationFormContext } from "@pages/configuration/forms/ConfigurationFormContext.tsx";

import { FormPath } from "@src/types/Form.ts";
import {
  ConfigurationFormDropzoneTag,
  ConfigurationFormMode
} from "@src/types/FormConfiguration.ts";
import { Question } from "@src/types/Question.ts";

import { ConfigurationQuestionBlock } from "./question/ConfigurationQuestionBlock/ConfigurationQuestionBlock.tsx";

export const ConfigurationFormQuestions = ({
  content,
  path,
  handleRemoveQuestion,
  handleDuplicateQuestion,
  mode
}: {
  content: Question[];
  path: Prop[];
  handleDuplicateQuestion?: (index: number) => void;
  handleRemoveQuestion?: (index: number) => void;
  mode: ConfigurationFormMode;
}) => {
  const { questionPath, updateQuestionHash } = useContext(
    ConfigurationFormContext
  );

  const handleSelectQuestion = useCallback(
    (path: FormPath) => {
      const newPath = path.join(".");
      if (questionPath === newPath) {
        updateQuestionHash("none");
      } else {
        updateQuestionHash(newPath);
      }
    },
    [questionPath, updateQuestionHash]
  );

  if (!content.length) {
    return (
      <DraggableItem
        index={0}
        dropzoneTags={[
          ConfigurationFormDropzoneTag.REGULAR,
          path.slice(0, -1).join(".")
        ]}
        canDropOver={data => {
          return (
            data.source.context.dropzoneTags[0] ===
              ConfigurationFormDropzoneTag.REGULAR ||
            data.source.context.dropzoneTags[0] ===
              ConfigurationFormDropzoneTag.MULTILEVEL
          );
        }}
      >
        <Box width="100" padding="050" />
      </DraggableItem>
    );
  }

  return (
    <>
      QUESTION -- TOP LEVEL
      {content?.map((question, index) => {
        const qPath = [...path, index];
        const questionAccessor = qPath.join(".");
        return (
          <DraggableItem
            index={index}
            key={questionAccessor}
            dropzoneTags={[
              ConfigurationFormDropzoneTag.REGULAR,
              path.slice(0, -1).join("."),
              question.type
            ]}
            canDropOver={data => {
              return (
                data.source.context.dropzoneTags[0] ===
                  ConfigurationFormDropzoneTag.REGULAR ||
                data.source.context.dropzoneTags[0] ===
                  ConfigurationFormDropzoneTag.MULTILEVEL
              );
            }}
          >
            <Inline
              gap="100"
              width="100"
              position="relative"
              spaceBetween={true}
              key={question.id}
            >
              <ConfigurationQuestionBlock
                style={{ width: "100%" }}
                showReorder
                question={question}
                mode={mode}
                key={questionAccessor}
                path={qPath}
                onClick={handleSelectQuestion}
                selectedQuestionPath={questionPath}
                handleDelete={() => handleRemoveQuestion?.(index)}
                handleDuplicate={() => handleDuplicateQuestion?.(index)}
              />
            </Inline>
          </DraggableItem>
        );
      })}
    </>
  );
};

ConfigurationFormQuestions.displayName = "ConfigurationFormQuestions";
