import { useMemo } from "react";

import { EChartsOption } from "echarts-for-react";

import { ChartConfig, ChartSubType, ChartType } from "./ChartTypes";

interface DataItem {
  [key: string]: string | number;
}

export const useGraphData = (
  data: DataItem[],
  config: ChartConfig,
  chartOptions: EChartsOption
) => {
  return useMemo(() => {
    // Handle pie charts
    if (config.type === ChartType.PIE) {
      // Handle row selection for pie charts
      if (config.swapColumns && config.rowIndex !== undefined) {
        const selectedRow = data[config.rowIndex];
        if (!selectedRow) return { series: [] };

        // Transform row data into pie chart format
        return {
          series: [
            {
              ...chartOptions?.series,
              name: config.value[0] || "",
              data: Object.entries(selectedRow)
                .filter(([key]) => {
                  // Include columns specified in label array, or exclude value columns
                  if (config.label.length > 0) {
                    return config.label.includes(key);
                  }
                  return key !== config.value[0];
                })
                .map(([key, value]) => ({
                  name: key,
                  value: Number(value)
                }))
            }
          ]
        };
      }

      // Standard pie chart logic
      return {
        series: [
          {
            name: config.value[0] || "",
            ...chartOptions?.series,
            data: data.map(item => ({
              name: String(item[config.label[0]] || ""),
              value: Number(item[config.value[0]] || 0)
            }))
          }
        ]
      };
    }

    // Handle line and bar charts (cartesian charts)
    if (config.type === ChartType.LINE || config.type === ChartType.BAR) {
      // Handle swapped columns (transpose data)
      if (config.swapColumns) {
        return {
          xAxis: {
            data: config.xAxis
          },
          series: data.map(item => ({
            ...chartOptions?.series,
            name: String(item[config.series[0]] || ""),
            data: config.xAxis.map(key => Number(item[key] || 0))
          }))
        };
      }

      // Standard cartesian chart logic
      return {
        xAxis: {
          data: data.map(item => String(item[config.xAxis[0]] || ""))
        },
        series: config.series.map(seriesKey => ({
          ...chartOptions?.series,
          name: seriesKey,
          data: data.map(item => Number(item[seriesKey] || 0))
        }))
      };
    }

    return { series: [] };
  }, [data, config]);
};
