import { EChartsOption } from "echarts-for-react";

import { ChartAxisType, ChartSubType, ChartType } from "./ChartTypes";

// In a chartConfig.ts file
export const getBaseOptions = ({
  title,
  type,
  showToolbox
}: {
  title?: string;
  type?: ChartType;
  showToolbox?: boolean;
}): EChartsOption => {
  // returns EChartOptions
  // Colors, fonts, animations etc. for onetheme
  return {
    title: {
      text: title,
      textStyle: {
        fontFamily:
          "'Noto Sans', Inter, system-ui, Avenir, Helvetica, Arial, sans-serif",
        fontSize: "16px"
      }
    },
    tooltip: {
      trigger: "axis"
    },
    toolbox: {
      show: showToolbox,
      feature: {
        dataView: { readOnly: false },
        magicType: { type: [ChartType.LINE, ChartType.BAR] },
        restore: {},
        saveAsImage: {}
      }
    },
    grid: {
      left: "5%",
      right: "5%",
      bottom: "10%",
      top: "15%",
      containLabel: true
    },
    legend: {
      orient: "horizontal",
      bottom: "0"
    },
    series: {
      type: type
    }
  };
};

export const getOptionsForType: {
  [key in ChartType]: () => EChartsOption;
} = {
  [ChartType.LINE]: () => ({
    yAxis: {
      type: ChartAxisType.VALUE
    },
    xAxis: {
      type: ChartAxisType.CATEGORY,
      axisLabel: {
        interval: 0 // Show all labels
      }
    }
  }),
  [ChartType.BAR]: () => ({
    yAxis: {
      type: ChartAxisType.VALUE
    },
    xAxis: {
      type: ChartAxisType.CATEGORY,
      axisLabel: {
        interval: 0
      }
    }
  }),
  [ChartType.PIE]: () => ({
    tooltip: {
      trigger: "item"
    },
    series: {
      radius: "50%",
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: "rgba(0, 0, 0, 0.5)"
        }
      }
    }
  })
};

export const getOptionsForSubType: {
  [key in ChartType]: ({ subType }: { subType: ChartSubType }) => EChartsOption;
} = {
  [ChartType.LINE]: ({ subType }) => {
    switch (subType) {
      case ChartSubType.SMOOTH_LINE:
        return {
          series: {
            smooth: true
          }
        };
      case ChartSubType.STACKED_LINE:
        return {
          series: {
            stack: "Total"
          }
        };
      case ChartSubType.STACKED_AREA_LINE:
        return {
          series: {
            stack: "Total",
            areaStyle: {},
            emphasis: {
              focus: "series"
            }
          }
        };
      default:
        return {};
    }
  },
  [ChartType.BAR]: ({ subType }) => {
    // specific options for basic bar chart
    switch (subType) {
      case ChartSubType.STACKED_BAR:
        return {
          series: {
            stack: "Total"
          }
        };
      default:
        return {};
    }
  },
  [ChartType.PIE]: ({ subType }) => {
    // specific options for basic pie chart
    switch (subType) {
      case ChartSubType.DONUT_PIE:
        return {
          series: {
            radius: ["40%", "70%"],
            avoidLabelOverlap: false,
            label: {
              show: false,
              position: "center"
            },
            emphasis: {
              label: {
                show: true,
                fontSize: 24,
                fontWeight: "bold"
              }
            }
          }
        };
      default:
        return {};
    }
  }
};
