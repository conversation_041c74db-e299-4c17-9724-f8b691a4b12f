import { EChartsOption } from "echarts-for-react";

import { BoxProps } from "../../fermions";

// For ChartTypes.ts
export enum ChartType {
  LINE = "line",
  BAR = "bar",
  PIE = "pie"
}

export enum ChartSubType {
  BASIC_LINE = "basicLine",
  SMOOTH_LINE = "smoothLine",
  STACKED_LINE = "stackedLine",
  STACKED_AREA_LINE = "stackedAreaLine",
  BASIC_BAR = "basicBar",
  HORIZONTAL_BAR = "horizontalBar",
  STACKED_BAR = "stackedBar",
  BASIC_PIE = "basicPie",
  DONUT_PIE = "donutPie"
}

enum ChartColorPalette {
  // Note this for later - for now we can just pass an array of 20 colors for example
  PALETTE_1 = "palette1",
  PALETTE_2 = "palette2",
  PALETTE_3 = "palette3",
  PALETTE_4 = "palette4",
  PALETTE_5 = "palette5"
}

export enum ChartAxisType {
  VALUE = "value",
  CATEGORY = "category"
}

type ChartSubTypeMap = {
  [ChartType.LINE]:
    | ChartSubType.BASIC_LINE
    | ChartSubType.SMOOTH_LINE
    | ChartSubType.STACKED_LINE
    | ChartSubType.STACKED_AREA_LINE;
  [ChartType.BAR]:
    | ChartSubType.BASIC_BAR
    | ChartSubType.STACKED_BAR
    | ChartSubType.HORIZONTAL_BAR;
  [ChartType.PIE]: ChartSubType.BASIC_PIE | ChartSubType.DONUT_PIE;
};

// const defaultChartSubType: { [key in ChartType]: ChartSubType } = {
//   [ChartType.LINE]: ChartSubType.BASIC_LINE,
//   [ChartType.BAR]: ChartSubType.BASIC_BAR,
//   [ChartType.PIE]: ChartSubType.BASIC_PIE
// };

interface BaseChartConfig<T extends ChartType> {
  type: T;
  subType: ChartSubTypeMap[T];
  swapColumns?: boolean;
  showToolbox?: boolean;
}

interface CartesianChartConfig<T extends ChartType.LINE | ChartType.BAR>
  extends BaseChartConfig<T> {
  xAxis: string[];
  series: string[];
}

interface PieChartConfig extends BaseChartConfig<ChartType.PIE> {
  label: string[];
  value: string[];
  rowIndex?: number; // For pie chart with row selection
}

export type ChartConfig =
  | CartesianChartConfig<ChartType.LINE>
  | CartesianChartConfig<ChartType.BAR>
  | PieChartConfig;

export type ChartProps = {
  title?: string;
  data: Array<{ [key: string]: any }>;
  config: ChartConfig;

  // For box
  width?: BoxProps["width"];
  height?: BoxProps["height"];
  margin?: BoxProps["margin"];
  padding?: BoxProps["padding"];
  style?: BoxProps["style"];

  // LATER v2
  // Other props could be things like: `hasDownloadButton`, `hasExportButton`, etc.
  // colorPalette?: `${ChartColorPalette}`;
  advancedOptions?: EChartsOption; // EChartOptions
};
