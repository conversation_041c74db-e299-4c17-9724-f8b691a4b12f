import { EChartsOption } from "echarts-for-react";

import { BoxProps } from "../../fermions";

// For ChartTypes.ts
export enum ChartType {
  LINE = "line",
  BAR = "bar",
  PIE = "pie"
}

export enum ChartSubType {
  BASIC_LINE = "basicLine",
  SMOOTH_LINE = "smoothLine",
  STACKED_LINE = "stackedLine",
  STACKED_AREA_LINE = "stackedAreaLine",
  BASIC_BAR = "basicBar",
  STACKED_BAR = "stackedBar",
  BASIC_PIE = "basicPie",
  DONUT_PIE = "donutPie"
}

// Temp color palette, will update it for V2
export enum ChartColorPalette {
  PALETTE_1 = "#A6EDB7",
  PALETTE_2 = "#C4C3FC",
  PALETTE_3 = "#FFF476",
  PALETTE_4 = "#1163D0",
  PALETTE_5 = "#55AA6D",
  PALETTE_6 = "#F6B541",
  PALETTE_7 = "#8280FF",
  PALETTE_8 = "#093268",
  PALETTE_9 = "#17652E",
  PALETTE_10 = "#F16D49",
  PALETTE_11 = "#504EBC",
  PALETTE_12 = "#CFE0F6"
}

export enum ChartAxisType {
  VALUE = "value",
  CATEGORY = "category"
}

export type ChartSubTypeMap = {
  [ChartType.LINE]:
    | ChartSubType.BASIC_LINE
    | ChartSubType.SMOOTH_LINE
    | ChartSubType.STACKED_LINE
    | ChartSubType.STACKED_AREA_LINE;
  [ChartType.BAR]: ChartSubType.BASIC_BAR | ChartSubType.STACKED_BAR;
  [ChartType.PIE]: ChartSubType.BASIC_PIE | ChartSubType.DONUT_PIE;
};

export const defaultSubType: { [key in ChartType]: ChartSubType } = {
  [ChartType.LINE]: ChartSubType.BASIC_LINE,
  [ChartType.BAR]: ChartSubType.BASIC_BAR,
  [ChartType.PIE]: ChartSubType.BASIC_PIE
};

interface BaseChartConfig<T extends ChartType> {
  type: T;
  subType?: ChartSubTypeMap[T];
  swapColumns?: boolean;
  showToolbox?: boolean;
}

interface CartesianChartConfig<T extends ChartType.LINE | ChartType.BAR>
  extends BaseChartConfig<T> {
  xAxis: string[];
  series: string[];
}

interface PieChartConfig extends BaseChartConfig<ChartType.PIE> {
  label: string[];
  value: string;
  rowIndex?: number; // For pie chart with row selection
}

export type ChartConfig =
  | CartesianChartConfig<ChartType.LINE>
  | CartesianChartConfig<ChartType.BAR>
  | PieChartConfig;

export type ChartData = Array<{ [key: string]: string }>;

export type ChartProps = {
  title?: string;
  data: ChartData;
  config: ChartConfig;

  // For box
  width?: BoxProps["width"];
  height?: BoxProps["height"];
  margin?: BoxProps["margin"];
  padding?: BoxProps["padding"];
  style?: BoxProps["style"];
  advancedOptions?: EChartsOption;

  // LATER v2
  // Other props could be things like: `hasDownloadButton`, `hasExportButton`, etc.
  // colorPalette?: `${ChartColorPalette}`;
};
