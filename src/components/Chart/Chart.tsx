import React from "react";

import ReactECharts from "echarts-for-react";
import { merge } from "lodash";

import { Box } from "../../fermions";
import {
  getBaseOptions,
  getOptionsForSubType,
  getOptionsForType
} from "./ChartOptions";
import { ChartProps } from "./ChartTypes";
import { useGraphData } from "./useChart";

export const Chart = ({
  title,
  data,
  config,
  advancedOptions,
  ...rest
}: ChartProps) => {
  const baseOptions = getBaseOptions({
    title,
    type: config?.type,
    showToolbox: config?.showToolbox
  });
  const typeOptions = getOptionsForType[config?.type]?.();
  const subTypeOptions = getOptionsForSubType[config?.type]?.({
    subType: config?.subType
  });
  const chartOptions = merge(
    {},
    baseOptions,
    typeOptions,
    subTypeOptions,
    advancedOptions
  );
  const graphData = useGraphData(data, config, chartOptions);
  const optionWithData = merge({}, chartOptions, graphData);

  return (
    <Box className="chart" {...rest}>
      <ReactECharts
        option={optionWithData}
        style={{ width: "100%", height: "100%" }}
      />
    </Box>
  );
};
