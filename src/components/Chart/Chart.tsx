import React from "react";

import { EChartsOption } from "echarts";
import ReactECharts from "echarts-for-react";
import { extend, merge } from "lodash";

import { Box, BoxProps } from "../../fermions";
import { getBaseOptions, getOptionsForSubType } from "./ChartOptions";
import { ChartProps, ChartType } from "./ChartTypes";
import { useGraphData } from "./useChart";

export interface TestGraphProps extends BoxProps {
  option?: EChartsOption;
  config: any;
  values: any;
}

export const Chart = ({
  title,
  description,
  data,
  config,
  advancedOptions,
  ...rest
}: ChartProps) => {
  const baseOptions = getBaseOptions({
    title,
    type: config?.type,
    showToolbox: config?.showToolbox
  });
  const typeOptions = getOptionsForSubType[config?.type]?.({
    subType: config?.subType
  });
  const chartOptions = merge({}, baseOptions, typeOptions, advancedOptions);
  console.log("Chart options:", chartOptions);
  const graphData = useGraphData(data, config, chartOptions);
  const optionWithData = merge({}, chartOptions, graphData);
  console.log("Option with data:", optionWithData);

  return (
    <Box className="graph" {...rest}>
      <ReactECharts
        option={optionWithData}
        style={{ width: "100%", height: "100%" }}
      />
    </Box>
  );
};
