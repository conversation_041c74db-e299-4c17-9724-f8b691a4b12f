import { useCallback, useMemo } from "react";

const HASH_KEY = "question";

function minimalHash(path: string) {
  return path
    .split(".")
    .filter(s => !["forms", "content", "properties"].includes(s))
    .join(".");
}

export function fullPath(hash: string | undefined) {
  const pathItems = hash?.split(".");
  if (!pathItems || pathItems.length < 4) {
    return "none";
  }

  const result = ["forms"];
  pathItems.forEach((item, index) => {
    if (["items", "columns", "charts"].includes(item)) {
      result.push(`properties.${item}`);
    } else if (
      index !== 0 &&
      !["items", "columns", "charts"].includes(pathItems[index - 1])
    ) {
      result.push("content");
      result.push(item);
    } else {
      result.push(item);
    }
  });
  return result.join(".");
}

export const useQuestionHash = (
  updateUrlHash: (k: string, v: string) => void,
  urlHashDetail: URLSearchParams
) => {
  const questionPath = useMemo(() => {
    const hash = urlHashDetail?.get?.(HASH_KEY) as string;
    if (hash === "none") {
      return "none";
    }
    return fullPath(hash);
  }, [urlHashDetail]);

  const removeQuestionHash = useCallback(() => {
    updateUrlHash(HASH_KEY, "none");
  }, [updateUrlHash]);

  const updateQuestionHash = useCallback(
    (path: string) => {
      updateUrlHash(HASH_KEY, minimalHash(path));
    },
    [updateUrlHash]
  );

  return { questionPath, removeQuestionHash, updateQuestionHash };
};

useQuestionHash.displayName = "useQuestionHash";
