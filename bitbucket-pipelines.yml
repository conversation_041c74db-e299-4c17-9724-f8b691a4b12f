image: node:22.18.0-bookworm
options:
  max-time: 20
definitions:
  caches:
    sonar: ~/.sonar/cache # Caching SonarCloud artifacts will speed up your build
  services:
    docker-for-sonar-pipe:
      memory: 14336
      type: docker
  steps:
    - step: &stop-previous-pipeline
        name: Stop previous Pipeline
        script:
          - npm install -g npm@10.9.2
          - rm -rf package-lock.json # Removing package.json and package-lock.json because for this step only zx and axios are needed (improves speed of the step)
          - rm -rf package.json
          - npm i zx axios --no-save
          - ./scripts/stopPreviousPipeline.mjs
    - step: &run-storybook-tests
        name: run storybook tests
        size: 4x
        caches:
          - node
        script:
          - export STORYBOOK_DISABLE_TELEMETRY=1
          - npm install -g npm@10.9.2
          - npm clean-install --ignore-scripts
          - npx playwright install --with-deps
          - npm run build-storybook
          - node --max-old-space-size=512 `which npx`  concurrently -k -s first -n "SB,TEST" -c "magenta,blue" \
            "node --max-old-space-size=512 `which npx` http-server storybook-static --port 6006 --silent" \
            "npx wait-on tcp:6006 && node --max-old-space-size=2048 `which npm` run test-storybook -- --ci --coverage --coverageDirectory=coverage/ui/storybook --testTimeout=60000"
        artifacts:
          - coverage/ui/storybook/**
    - step: &run-tests
        name: Run tests
        caches:
          - node
        script:
          - npm install -g npm@10.9.2
          - npm install
          - npm run test:ci
        artifacts:
          - coverage/unit/**
    - step: &merge-coverage-reports
        name: Merge Coverage Reports
        script:
          - npm install -g nyc
          - mkdir -p coverage/tmp
          - mv coverage/ui/storybook/coverage-storybook.json coverage/tmp
          - mv coverage/unit/coverage-final.json coverage/tmp
          - mkdir -p coverage/merged
          - npx nyc merge coverage/tmp coverage/merged/coverage.json
          - npx nyc report --reporter=lcov --temp-dir coverage/merged --report-dir coverage/merged
        artifacts:
          - coverage/merged/**
    - step: &code-analysis
        name: SonarCloud Scan and Analysis
        size: 4x
        clone:
          depth: full # SonarCloud scanner needs the full history to assign issues properly
        caches:
          - sonar
        services:
          - docker-for-sonar-pipe
        script:
          - echo "sonar will run in after-script"
        after-script:
          - pipe: sonarsource/sonarcloud-scan:4.1.0
            variables:
              EXTRA_ARGS: "-Dsonar.javascript.lcov.reportPaths=coverage/merged/lcov.info"
              DEBUG: "true"
          - pipe: sonarsource/sonarcloud-quality-gate:0.2.0
    - step: &version-onetheme
        name: Version OneTheme
        caches:
          - node
        script:
          # Check if the version has been bumped already. If so, skip the versioning step.
          - apt-get update && apt-get install -y curl jq git
          - export LAST_COMMIT_MESSAGE=$(git log -1 --pretty=%B)
          - echo $LAST_COMMIT_MESSAGE
          - if [[ $LAST_COMMIT_MESSAGE == "OA-758 Bump version to"* ]]; then echo "Version already bumped. Skipping versioning step."; exit 0; fi
            # Start versioning steps
          - ./scripts/versionOneTheme.sh
    - step: &pack-and-publish-to-bitbucket
        name: Pack and Publish to Bitbucket
        caches:
          - node
        script:
          - npm install -g npm@10.9.2
          - npm pack
          - export VERSION=$(node -p "require('./package.json').version")
          - mv "oneteam-onetheme-${VERSION}.tgz" "oneteam-onetheme-otai-${VERSION}.tgz"
          - pipe: atlassian/bitbucket-upload-file:0.7.5
            variables:
              BITBUCKET_USERNAME: $BITBUCKET_USERNAME
              BITBUCKET_APP_PASSWORD: $BITBUCKET_APP_PASSWORD
              FILENAME: "oneteam-onetheme-otai-${VERSION}.tgz"
              ACCOUNT: "devops-martinit"
              REPOSITORY: "onetheme"
          - npm pack
          - mv "oneteam-onetheme-${VERSION}.tgz" "oneteam-onetheme-oneteam-${VERSION}.tgz"
          - pipe: atlassian/bitbucket-upload-file:0.7.5
            variables:
              BITBUCKET_USERNAME: $BITBUCKET_USERNAME
              BITBUCKET_APP_PASSWORD: $BITBUCKET_APP_PASSWORD
              FILENAME: "oneteam-onetheme-oneteam-${VERSION}.tgz"
              ACCOUNT: "devops-martinit"
              REPOSITORY: "onetheme"
        artifacts:
          - "*.tgz"
    - step: &build-and-publish-storybook-all-themes
        name: Build ALL Storybook Themes and Deploy to Azure
        size: 2x
        caches:
          - node
        script:
          - npm install -g npm@10.9.2
          - THEMES="otai,oneteam"
          - for theme in $(echo $THEMES | sed "s/,/ /g"); do
            ./scripts/buildStorybook.sh $theme;
            done
          - export AZURE_STORAGE_ACCESS_KEY=$(cat secret/sas.txt)
          - pipe: microsoft/azure-storage-deploy:2.0.1
            variables:
              SOURCE: "storybook-static"
              DESTINATION: "https://${STORYBOOK_AZURE_STORAGE_ACCOUNT_NAME}.blob.core.windows.net/static-assets/otai/storybook"
              DESTINATION_SAS_TOKEN: "/?${AZURE_STORAGE_ACCESS_KEY}"
    - step: &generateSASToken
        name: generate SAS Token
        image: mcr.microsoft.com/azure-cli:cbl-mariner2.0
        script:
          - tdnf install -y nodejs
          - npm install -g zx
          - zx ./scripts/getSASToken.mjs > sas.txt
          - mkdir secret
          - mv sas.txt secret
        artifacts:
          - secret/**
    - step: &build
        name: Build OneTheme Storybook
        caches:
          - node
        script:
          - npm install
          - npm run build
    - step: &docker-build-push-storybook-app
        name: Build and Push Docker Image
        caches:
          - docker
        services:
          - docker
        clone:
          depth: full
        script:
          - ./scripts/buildAndPublishStorybookApp.sh
        artifacts:
          - version-storybook-app.txt
    - step: &deploy-storybook-app
        name: Deploy To App Service
        script:
          - export DOCKER_DEFAULT_PLATFORM=linux/amd64
          - export VERSION=$(cat ./version-storybook-app.txt)
          - IMAGE="$ONETEAM_DEV_CONTAINER_REGISTRY_SERVER/$ONETHEME_STORYBOOK_CONTAINER_REGISTRY_REPO:$VERSION"
          - pipe: atlassian/azure-cli-run:1.2.4
            variables:
              AZURE_TENANT_ID: ${AZURE_TENANT_ID}
              AZURE_APP_ID: ${AZURE_APP_ID}
              AZURE_PASSWORD: ${AZURE_PASSWORD}
              CLI_COMMAND: >
                az webapp config container set
                --subscription oneteam_dev
                --resource-group onetheme-storybook
                --name onetheme-storybook
                --docker-custom-image-name ${IMAGE}
              DEBUG: "true"

pipelines:
  custom:
    deploy-storybook-app:
      - step: *docker-build-push-storybook-app
      - step: *deploy-storybook-app
  branches:
    "main":
      - parallel:
          - step: *run-storybook-tests
          - step: *build
          - step: *run-tests
      - step: *merge-coverage-reports
      - step: *code-analysis
      - step: *version-onetheme
  pull-requests:
    "**":
      - parallel:
          - step: *stop-previous-pipeline
          - step: *build
          - step: *run-storybook-tests
          - step: *run-tests
      - step: *merge-coverage-reports
      - step: *code-analysis
  tags:
    "**":
      - parallel:
          - step: *build
          - step: *generateSASToken
      - parallel:
          - step: *build-and-publish-storybook-all-themes
          - step: *pack-and-publish-to-bitbucket
